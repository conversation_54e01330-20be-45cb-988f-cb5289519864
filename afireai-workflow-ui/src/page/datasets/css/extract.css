.datasetExtractRoot {
    width: 100%;
    height: 100%;
    background: #f5f6fa;
    overflow: auto;
    display: flex;
    flex-direction: column;
    padding: 20px;
    margin: 0 auto;
}

.datasetExtractContainer {
    width: 100%;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
}


/* 顶部导航和步骤指示器 */

.datasetExtractTopBar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    width: 100%;
    padding: 10px 20px;
    background-color: #f5f6fa;
    position: relative;
}

.datasetExtractBackButton {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    margin-left: 0;
}

.datasetExtractBackButton:hover {
    color: #4a90e2;
}


/* 步骤指示器 */

.datasetExtractSteps {
    display: flex;
    align-items: center;
    margin-left: auto;
    margin-right: auto;
}

.datasetExtractStepItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    margin: 0 10px;
}

.datasetExtractStepBadge {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #e0e0e0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 6px;
    font-size: 10px;
}

.datasetExtractStepBadge.active {
    background-color: #4a90e2;
    color: white;
}

.datasetExtractStepBadge.inactive {
    background-color: #e0e0e0;
    color: #666;
}

.datasetExtractStepText {
    font-size: 11px;
    color: #666;
}

.datasetExtractStepText.active {
    color: #4a90e2;
    font-weight: bold;
}

.datasetExtractStepLine {
    width: 20px;
    height: 1px;
    background-color: #e0e0e0;
    margin: 0 5px;
}


/* 主要内容 */

.datasetExtractContent {
    background: #f5f6fa;
    padding: 0 20px;
    width: 100%;
    overflow-y: auto;
    height: calc(100vh - 100px);
    display: flex;
}

.datasetExtractLeftContent {
    flex: 1;
    padding-right: 20px;
}

.datasetExtractRightContent {
    width: 50%;
    padding-left: 20px;
}


/* 文件列表 */

.datasetExtractFileList {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    flex: 1;
    height: calc(100vh - 100px);
    overflow-y: auto;
    margin-left: 20px;
    width: 50%;
    display: flex;
    flex-direction: column;
}


/* 文件列表标题 */

.datasetExtractFileListTitle {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}


/* 文件项 */

.datasetExtractFileItem {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.datasetExtractFileItem:hover {
    background-color: #f9f9f9;
}

.datasetExtractFileIcon {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    margin-right: 10px;
    background-color: #f5f5f5;
}

.datasetExtractFileIcon.pdf {
    background-color: #ffebee;
    color: #f44336;
}

.datasetExtractFileIcon.excel {
    background-color: #e8f5e9;
    color: #4caf50;
}

.datasetExtractFileIcon.word {
    background-color: #e3f2fd;
    color: #2196f3;
}

.datasetExtractFileIcon.powerpoint {
    background-color: #fff3e0;
    color: #ff9800;
}

.datasetExtractFileIcon.text {
    background-color: #f5f5f5;
    color: #607d8b;
}

.datasetExtractFileInfo {
    flex: 1;
}

.datasetExtractFileName {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.datasetExtractFileDetails {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 11px;
    color: #888;
}

.datasetExtractFileStatus {
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

.datasetExtractFileStatus.complete {
    background-color: #e8f5e9;
    color: #4caf50;
}

.datasetExtractFileStatus.processing {
    background-color: #fff8e1;
    color: #ffc107;
}

.datasetExtractFileActions {
    display: flex;
    gap: 4px;
}

.datasetExtractFileActionButton {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 14px;
}

.datasetExtractFileActionButton:hover {
    background-color: #f0f0f0;
    color: #333;
}

.datasetExtractFileActionButton.delete:hover {
    color: #f44336;
}


/* 搜索设置部分 */

.datasetExtractSearchSettings {
    margin-bottom: 20px;
}

.datasetExtractSearchSettingsInfo {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #666;
}

.datasetExtractSearchSettingsLink {
    color: #4a90e2;
    text-decoration: none;
}

.datasetExtractSearchSettingsLink:hover {
    text-decoration: underline;
}

.datasetExtractSearchBox {
    border-radius: 8px;
    background-color: #FAFAFA;
    padding: 20px;
}

.datasetExtractVectorSearch {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    background-color: #F5F5FF;
    border-radius: 8px;
    padding: 15px;
}

.datasetExtractVectorSearchIcon {
    margin-right: 15px;
}

.datasetExtractVectorSearchContent {
    flex: 1;
}

.datasetExtractVectorSearchTitle {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.datasetExtractVectorSearchDescription {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
}

.datasetExtractRerankOption {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #E0E0E0;
}

.datasetExtractRerankLabel {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.datasetExtractCheckbox {
    width: 16px;
    height: 16px;
}

.datasetExtractRerankText {
    font-size: 14px;
    color: #333;
}

.datasetExtractRerankInfo {
    color: #999;
    font-size: 14px;
    cursor: help;
}

.datasetExtractSearchControls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.datasetExtractSearchControlItem {
    flex: 1;
    min-width: 200px;
}

.datasetExtractSearchControlLabel {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
}

.datasetExtractSearchControlInfo {
    color: #999;
    font-size: 14px;
    cursor: help;
}

.datasetExtractSearchControlValue {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.datasetExtractSearchControlInputWrapper {
    display: flex;
    align-items: center;
}

.datasetExtractSearchControlInput {
    width: 60px;
    height: 32px;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    padding: 0 10px;
    font-size: 14px;
    color: #333;
    text-align: center;
}

.datasetExtractSearchControlButtons {
    display: flex;
    flex-direction: column;
    margin-left: 5px;
}

.datasetExtractSearchControlButton {
    width: 20px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #F5F5F5;
    border: 1px solid #E0E0E0;
    cursor: pointer;
    padding: 0;
    font-size: 10px;
}

.datasetExtractSearchControlButton:first-child {
    border-radius: 2px 2px 0 0;
    border-bottom: none;
}

.datasetExtractSearchControlButton:last-child {
    border-radius: 0 0 2px 2px;
}

.datasetExtractSearchControlSlider {
    width: 100%;
    height: 4px;
    -webkit-appearance: none;
    appearance: none;
    background: #E0E0E0;
    outline: none;
    border-radius: 2px;
}

.datasetExtractSearchControlSlider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: #4a90e2;
    border-radius: 2px;
    cursor: pointer;
}

.datasetExtractSearchControlSlider::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: #4a90e2;
    border-radius: 2px;
    cursor: pointer;
}


/* 自定义滑块样式 */

.customRangeSlider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 2px;
    background-color: transparent;
    outline: none;
    margin: 0;
    border: none;
    position: relative;
}

.customRangeSlider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 2px;
    height: 14px;
    background: #4a90e2;
    cursor: pointer;
    border-radius: 0;
    margin-top: -6px;
    box-shadow: none;
}

.customRangeSlider::-moz-range-thumb {
    width: 2px;
    height: 14px;
    background: #4a90e2;
    cursor: pointer;
    border: none;
    border-radius: 0;
    margin-top: -6px;
    box-shadow: none;
}


/* 为滑动条添加蓝色活动部分 */

.customRangeSlider::-webkit-slider-runnable-track {
    background: linear-gradient(to right, #4a90e2 var(--value, 50%), #E0E0E0 var(--value, 50%));
    height: 2px;
    border-radius: 0;
}

.customRangeSlider::-moz-range-track {
    background: linear-gradient(to right, #4a90e2 var(--value, 50%), #E0E0E0 var(--value, 50%));
    height: 2px;
    border-radius: 0;
}


/* 全文検索部分 */

.fullTextSearchContainer {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #E6E8F0;
}

.fullTextSearchHeader {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.fullTextSearchIconContainer {
    margin-right: 15px;
    background-color: #7C3AED1A;
    border-radius: 8px;
    padding: 10px;
}

.fullTextSearchContent h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.fullTextSearchContent p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.rerankModelOption {
    background-color: #FFFFFF;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
    border: 1px solid #EEEEEE;
}

.rerankModelLabel {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.rerankModelCheckbox {
    width: 16px;
    height: 16px;
}

.rerankModelText {
    font-size: 14px;
    color: #333;
}

.rerankModelInfo {
    color: #999;
    font-size: 14px;
    cursor: help;
}

.searchControlsContainer {
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.searchControlItem {
    flex: 1;
}

.searchControlLabel {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    padding: 5px 0;
    font-size: 14px;
    font-weight: 500;
}

.searchControlInfo {
    margin-left: 5px;
    color: #999;
    font-size: 14px;
    cursor: help;
}

.searchControlInputGroup {
    display: flex;
    align-items: center;
    gap: 15px;
}

.numberInputContainer {
    display: flex;
    align-items: center;
    background-color: #F5F5F5;
    border-radius: 4px;
    padding: 0;
    width: 90px;
    border: none;
    height: 40px;
}

.numberInputWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    position: relative;
}

.numberInput {
    border: none;
    background-color: transparent;
    width: 40px;
    font-size: 16px;
    padding: 10px 0;
    text-align: center;
}

.numberInputButtons {
    position: absolute;
    right: 10px;
    display: flex;
    flex-direction: column;
}

.numberInputButton {
    border: none;
    background: none;
    cursor: pointer;
    padding: 2px;
    font-size: 10px;
}

.sliderContainer {
    flex: 1;
    display: flex;
    align-items: center;
    height: 40px;
    background-color: transparent;
}

.sliderInput {
    width: 100%;
}


/* Rerank模型部分 */

.datasetExtractRerankSection {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    background-color: #f9f9f9;
    margin-top: 0;
}

.datasetExtractRerankModel {
    display: flex;
    align-items: flex-start;
}

.datasetExtractRerankIcon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #e3f2fd;
    color: #4a90e2;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-right: 15px;
    flex-shrink: 0;
}

.datasetExtractRerankContent {
    flex: 1;
}

.datasetExtractRerankTitle {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.datasetExtractRerankDescription {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.datasetExtractRerankCheckbox {
    margin-left: 15px;
    display: flex;
    align-items: center;
}


/* 文件列表底部按钮 */

.datasetExtractFileListActions {
    display: flex;
    justify-content: space-between;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.datasetExtractSection {
    margin-bottom: 30px;
}

.datasetExtractSectionTitle {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
    text-align: left;
}


/* 卡片样式 */

.datasetExtractCard {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: white;
}

.datasetExtractCard:hover {
    border-color: #4a90e2;
    background-color: #f5f9ff;
}

.datasetExtractCard.selected {
    border-color: #4a90e2;
    background-color: #f5f9ff;
}

.datasetExtractCard.active {
    border-color: #4a90e2;
    background-color: #f5f9ff;
}

.datasetExtractCardHeader {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.datasetExtractCardIcon {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.datasetExtractCardIcon.blue {
    background-color: #e6f0ff;
    color: #4a90e2;
    box-shadow: 0 2px 4px rgba(74, 144, 226, 0.1);
}

.datasetExtractCardIcon.orange {
    background-color: #fff2e6;
    color: #ff9500;
    box-shadow: 0 2px 4px rgba(255, 149, 0, 0.1);
}

.datasetExtractCardIcon.purple {
    background-color: #f0e6ff;
    color: #9c27b0;
    box-shadow: 0 2px 4px rgba(156, 39, 176, 0.1);
}

.datasetExtractCardTitle {
    font-weight: bold;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.datasetExtractBadge {
    background-color: #e6f0ff;
    color: #4a90e2;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: normal;
}

.datasetExtractCardDescription {
    font-size: 13px;
    color: #666;
    margin-bottom: 15px;
}


/* 表单元素 */

.datasetExtractFormGroup {
    margin-bottom: 15px;
}

.datasetExtractFormLabel {
    font-size: 13px;
    color: #333;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.infoIcon {
    color: #999;
    font-size: 12px;
    cursor: help;
    position: relative;
}

.tooltipContainer {
    position: relative;
    display: inline-block;
}

.tooltip {
    position: absolute;
    top: -10px;
    left: 20px;
    transform: translateY(-100%);
    background-color: #333;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 12px;
    width: 280px;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    line-height: 1.5;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 10px;
    width: 10px;
    height: 10px;
    background-color: #333;
    transform: rotate(45deg);
}

.datasetExtractFormInput {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.datasetExtractFormRow {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.datasetExtractFormItem {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.datasetExtractTextInput {
    width: 100px;
    height: 28px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 12px;
    background-color: white;
}

.datasetExtractTextInput:focus {
    border-color: #4a90e2;
    background-color: #fff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.datasetExtractInputSuffix {
    margin-left: 10px;
    font-size: 13px;
    color: #666;
}

.datasetExtractCheckboxGroup {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.datasetExtractCheckboxLabel {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
}

.datasetExtractCheckbox {
    width: 16px;
    height: 16px;
}


/* 按钮 */

.datasetExtractButtonGroup {
    display: flex;
    gap: 10px;
}

.datasetExtractSecondaryButton {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    height: 32px;
}

.datasetExtractSecondaryButton:hover {
    background-color: #e0e0e0;
}

.datasetExtractPreviewButton {
    background-color: white;
    color: #4a90e2;
    border: 1px solid #4a90e2;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    height: 28px;
}

.datasetExtractPreviewButton:hover {
    background-color: #f5f9ff;
    border-color: #4a90e2;
}

.datasetExtractResetButton {
    background-color: white;
    color: #666;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    height: 28px;
}

.datasetExtractResetButton:hover {
    background-color: #f5f5f5;
}


/* 预览部分 */

.datasetExtractPreviewHeader {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
}

.datasetExtractPreviewFile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.datasetExtractPreviewFileIcon {
    color: #4a90e2;
    font-size: 16px;
    background-color: #e6f0ff;
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.datasetExtractPreviewFileName {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.datasetExtractPreviewFileCount {
    font-size: 12px;
    color: #666;
    margin-left: auto;
}

.datasetExtractPreviewContent {
    margin-top: 10px;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.datasetExtractPreviewEmpty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    text-align: center;
}

.datasetExtractPreviewEmptyIcon {
    font-size: 24px;
    color: #ccc;
    margin-bottom: 10px;
}

.datasetExtractPreviewEmptyText {
    font-size: 13px;
    color: #999;
    max-width: 300px;
    line-height: 1.5;
}

.datasetExtractPrimaryButton {
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    height: 32px;
}

.datasetExtractPrimaryButton:hover {
    background-color: #3a80d2;
}


/* 警告信息 */

.datasetExtractWarning {
    background-color: #fff9e6;
    border: 1px solid #ffe0b2;
    border-radius: 4px;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
    color: #ff9800;
    margin-top: 10px;
}

.warningIcon {
    color: #ff9800;
    font-size: 16px;
}


/* 模型选择器 */

.datasetExtractModelSelector {
    position: relative;
    margin-bottom: 15px;
}

.datasetExtractModelDisplay {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    background-color: white;
}

.datasetExtractModelDisplay:hover {
    border-color: #4a90e2;
}

.datasetExtractModelIcon {
    color: #4a90e2;
    font-size: 16px;
}

.datasetExtractModelName {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.datasetExtractModelArrow {
    color: #999;
    font-size: 12px;
}

.datasetExtractModelDropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100;
    margin-top: 5px;
    max-height: 300px;
    overflow-y: auto;
}

.datasetExtractModelSearchContainer {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.datasetExtractModelSearchIcon {
    color: #999;
    font-size: 14px;
}

.datasetExtractModelSearchInput {
    flex: 1;
    border: none;
    outline: none;
    font-size: 14px;
}

.datasetExtractModelGroup {
    padding: 10px;
}

.datasetExtractModelGroupTitle {
    font-size: 12px;
    color: #999;
    margin-bottom: 5px;
}

.datasetExtractModelOption {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.datasetExtractModelOption:hover {
    background-color: #F5F7FA;
}

.datasetExtractModelOption.selected {
    background-color: #F0F7FF;
}

.datasetExtractModelOptionIcon {
    color: #4a90e2;
    font-size: 16px;
}

.datasetExtractModelOptionName {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.datasetExtractModelOptionCheck {
    color: #4a90e2;
    font-size: 14px;
}


/* 滑块 */

.datasetExtractSliderGroup {
    margin-top: 15px;
}

.datasetExtractSliderRow {
    display: flex;
    gap: 30px;
    margin-top: 15px;
    align-items: flex-start;
}

.datasetExtractSliderItem {
    flex: 1;
}

.datasetExtractSliderLabel {
    font-size: 13px;
    color: #333;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.datasetExtractSliderContainer {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 8px;
}

.datasetExtractNumberInput {
    width: 60px;
    height: 36px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0 5px;
    font-size: 14px;
    text-align: center;
    background-color: #f5f5f5;
    position: relative;
}

.datasetExtractNumberInput::-webkit-inner-spin-button,
.datasetExtractNumberInput::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.datasetExtractNumberInput:focus {
    border-color: #4a90e2;
    background-color: #fff;
    outline: none;
}

.datasetExtractSlider {
    flex: 1;
    height: 8px;
    -webkit-appearance: none;
    appearance: none;
    background: #e0e0e0;
    outline: none;
    border-radius: 4px;
    margin: 0;
    padding: 0;
}

.datasetExtractSlider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 20px;
    border-radius: 2px;
    background: #4a90e2;
    cursor: pointer;
    margin-top: -6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.datasetExtractSlider::-moz-range-thumb {
    width: 12px;
    height: 20px;
    border-radius: 2px;
    background: #4a90e2;
    cursor: pointer;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.datasetExtractSlider::-webkit-slider-runnable-track {
    background: linear-gradient(to right, #4a90e2 var(--progress), #e0e0e0 var(--progress));
    height: 8px;
    border-radius: 4px;
}

.datasetExtractSlider::-moz-range-track {
    background: linear-gradient(to right, #4a90e2 var(--progress), #e0e0e0 var(--progress));
    height: 8px;
    border-radius: 4px;
}


/* 底部按钮 */

.datasetExtractActions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
}


/* 链接样式 */

.datasetExtractLink {
    color: #4a90e2;
    text-decoration: none;
}

.datasetExtractLink:hover {
    text-decoration: underline;
}

.datasetExtractSettingsInfo {
    font-size: 13px;
    color: #666;
    margin-bottom: 15px;
}


/* ハイブリッド検索 */

.hybridSearchSettings {
    margin-top: 15px;
    border-top: 1px solid #f0f0f0;
    padding-top: 15px;
}

.hybridSearchSettingsRow {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

.hybridSearchSettingsItem {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.hybridSearchSettingsIcon {
    color: #666;
    cursor: pointer;
    font-size: 14px;
}

.hybridSearchSettingsLabel {
    font-weight: 500;
    font-size: 14px;
    color: #333;
}

.hybridSearchSettingsInfo {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
}

.hybridSearchWeightSlider {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.hybridSearchWeightLabel {
    font-size: 13px;
    color: #666;
    width: 90px;
}

.hybridSearchWeightValue {
    font-size: 13px;
    color: #333;
    width: 30px;
    text-align: center;
}

.hybridSearchWeightInput {
    flex: 1;
    height: 8px;
    -webkit-appearance: none;
    appearance: none;
    background: #e0e0e0;
    outline: none;
    border-radius: 4px;
    margin: 0;
    padding: 0;
}

.hybridSearchWeightInput::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 20px;
    border-radius: 2px;
    background: #4a90e2;
    cursor: pointer;
    margin-top: -6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.hybridSearchWeightInput::-moz-range-thumb {
    width: 12px;
    height: 20px;
    border-radius: 2px;
    background: #4a90e2;
    cursor: pointer;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.hybridSearchWeightInput::-webkit-slider-runnable-track {
    background: linear-gradient(to right, #4a90e2 var(--progress), #e0e0e0 var(--progress));
    height: 8px;
    border-radius: 4px;
}

.hybridSearchWeightInput::-moz-range-track {
    background: linear-gradient(to right, #4a90e2 var(--progress), #e0e0e0 var(--progress));
    height: 8px;
    border-radius: 4px;
}

.hybridSearchControls {
    margin-bottom: 20px;
}

.hybridSearchControlsRow {
    display: flex;
    gap: 20px;
}

.hybridSearchControlItem {
    flex: 1;
}

.hybridSearchControlLabel {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    color: #333;
    margin-bottom: 8px;
}

.hybridSearchControlInput {
    display: flex;
    align-items: center;
    gap: 10px;
}

.hybridSearchRerankModel {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.hybridSearchRerankModelIcon {
    color: #4a90e2;
    font-size: 20px;
    margin-top: 2px;
}

.hybridSearchRerankModelInfo {
    flex: 1;
}

.hybridSearchRerankModelTitle {
    font-weight: 500;
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
}

.hybridSearchRerankModelDescription {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
}

.hybridSearchRerankModelToggle {
    margin-top: 2px;
}

.hybridSearchRerankModelCheckbox {
    width: 18px;
    height: 18px;
}

.hybridSearchCollapsed {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 15px;
    border-top: 1px solid #f0f0f0;
    padding-top: 15px;
    cursor: pointer;
}

.hybridSearchCollapsedIcon {
    color: #666;
    font-size: 14px;
}

.hybridSearchCollapsedLabel {
    font-weight: 500;
    font-size: 14px;
    color: #333;
}


/* 索引方法 */

.datasetExtractIndexMethods {
    display: flex;
    gap: 15px;
}

.datasetExtractIndexCard {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 15px;
    background-color: white;
    cursor: pointer;
    transition: all 0.2s;
    flex: 1;
}

.datasetExtractIndexCard:hover {
    border-color: #4a90e2;
    background-color: #f5f9ff;
}

.datasetExtractIndexCard.selected {
    border-color: #4a90e2;
    background-color: #f5f9ff;
}

.datasetExtractIndexCardHeader {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.datasetExtractIndexCardIcon {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    background-color: #f5f5f5;
    color: #666;
}

.datasetExtractIndexCardTitle {
    font-weight: bold;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.datasetExtractIndexCardDescription {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
}

.recommendBadge {
    background-color: #e6f0ff;
    color: #4a90e2;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: normal;
}