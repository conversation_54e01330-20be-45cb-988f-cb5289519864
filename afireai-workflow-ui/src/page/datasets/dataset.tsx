import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import "./dataset.css";
import { FaPlus, FaSearch, FaExternalLinkAlt, FaFolder, FaTag, FaEllipsisH } from "react-icons/fa";

const Dataset = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("knowledge");
  const [showAll, setShowAll] = useState(true);
  const [searchText, setSearchText] = useState("");
  const [selectedTag, setSelectedTag] = useState("all");

  const handleCreateClick = () => {
    navigate("/datasets/create");
  };

  // 示例数据集
  const datasets = [
    {
      id: 1,
      title: "社内業務DB",
      description: "1 ドキュメント | 256 単語 | リンクされたナレッジベース",
      icon: <FaFolder />
    },
    // 可以添加更多数据集
  ];

  return (
    <div className="datasetRoot">
      <div className="datasetContainer">
        {/* 顶部导航栏 */}
        <div className="datasetTabs">
          <button
            onClick={() => setActiveTab("knowledge")}
            className={
              activeTab === "knowledge"
                ? "datasetTabButton datasetTabButtonActive"
                : "datasetTabButton"
            }
          >
            ナレッジベース
          </button>
        </div>

        {/* 过滤和搜索区域 */}
        <div className="datasetFilterBar">
          <div className="datasetFilterOptions">
            <label className="datasetCheckboxLabel">
              <input
                type="checkbox"
                checked={showAll}
                onChange={() => setShowAll(!showAll)}
                className="datasetCheckbox"
              />
              ナレッジベース全体
            </label>

            <div className="datasetTagSelector" onClick={() => setSelectedTag(selectedTag === 'all' ? 'work' : 'all')}>
              <span>{selectedTag === 'all' ? 'すべてのタグ' : '勤怠'}</span>
              <span className="datasetDropdownIcon">▼</span>
            </div>
          </div>

          <div className="datasetSearchAndApi">
            <div className="datasetSearchBar">
              <FaSearch className="datasetSearchIcon" />
              <input
                type="text"
                placeholder="検索"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className="datasetSearchInput"
              />
            </div>

            <button className="datasetApiButton">
              <FaExternalLinkAlt className="datasetApiIcon" />
              外部ナレッジベース連携API
            </button>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="datasetContent">
          {/* 左侧创建区域 */}
          <div className="datasetCreateSection">
            <button className="datasetCreateButton" onClick={handleCreateClick}>
              <FaPlus className="datasetCreateIcon" />
              ナレッジベースを作成
            </button>
            <div className="datasetCreateDescription">
              独自のテキストデータをインポートする。LLMコンテキストが最適化のためにWebhookをクリアリアルタイムデータを書き込むことができます。
            </div>

          </div>

          {/* 右侧数据集列表 */}
          <div className="datasetList">
            {datasets.map((dataset) => (
              <div key={dataset.id} className="datasetCard">
                <div className="datasetCardHeader">
                  <div className="datasetCardIcon">{dataset.icon}</div>
                  <div className="datasetCardTitle">{dataset.title}</div>
                </div>
                <div className="datasetCardDescription">{dataset.description}</div>
                <div className="datasetCardFooter">
                  <div className="datasetCardTag">
                    <FaTag className="datasetCardTagIcon" />
                    <span>タグを追加</span>
                  </div>
                  <div className="datasetCardActions">
                    <FaEllipsisH />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dataset;
