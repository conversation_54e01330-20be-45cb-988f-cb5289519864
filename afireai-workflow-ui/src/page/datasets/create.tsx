import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import "./create.css";
import { FaArrowLeft, FaUpload, FaFileAlt, FaInfoCircle } from "react-icons/fa";

const DatasetCreate = () => {
  const navigate = useNavigate();
  const [isDragging, setIsDragging] = useState(false);
  const [createEmpty, setCreateEmpty] = useState(false);

  // 处理拖拽事件
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    // 这里可以处理文件上传逻辑
    const files = e.dataTransfer.files;
    console.log("Dropped files:", files);
  };

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      console.log("Selected files:", files);
    }
  };

  // 返回上一页
  const handleBack = () => {
    navigate("/datasets");
  };

  // 处理下一步
  const handleNext = () => {
    navigate("/datasets/extract");
  };

  return (
    <div className="datasetCreateRoot">
      <div className="datasetCreateContainer">
        {/* 顶部导航和步骤指示器 */}
        <div className="datasetCreateTopBar">
          <button className="datasetCreateBackButton" onClick={handleBack}>
            <FaArrowLeft />
            <span>ナレッジベース</span>
          </button>

          <div className="datasetCreateSteps">
            <div className="datasetCreateStepItem">
              <div className="datasetCreateStepBadge active">Step1</div>
              <div className="datasetCreateStepText active">データソース</div>
            </div>
            <div className="datasetCreateStepLine"></div>
            <div className="datasetCreateStepItem">
              <div className="datasetCreateStepBadge inactive">Step2</div>
              <div className="datasetCreateStepText">テキスト抽出</div>
            </div>
            <div className="datasetCreateStepLine"></div>
            <div className="datasetCreateStepItem">
              <div className="datasetCreateStepBadge inactive">Step3</div>
              <div className="datasetCreateStepText">索引と設定</div>
            </div>
          </div>
        </div>

        {/* 主要内容 */}
        <div className="datasetCreateContent">
          <h2 className="datasetCreateTitle">データソース</h2>

          {/* 数据源选项 */}
          <div className="datasetCreateOptions">
            <div className="datasetCreateOption">
              <div className="datasetCreateOptionIcon">
                <FaFileAlt />
              </div>
              <div className="datasetCreateOptionText">テキストファイルから</div>
            </div>
          </div>

          {/* 文件上传区域 */}
          <div className="datasetCreateUploadSection">
            <h3 className="datasetCreateSubtitle">テキストファイルをアップロード</h3>
            <div
              className={`datasetCreateDropzone ${isDragging ? 'dragging' : ''}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <div className="datasetCreateDropzoneContent">
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <FaUpload className="datasetCreateDropzoneIcon" />
                  <p className="datasetCreateDropzoneText">
                    ファイルまたはフォルダをドラッグアンドドロップする <span className="datasetCreateDropzoneHighlight">参照</span>
                  </p>
                </div>
                <p className="datasetCreateDropzoneFormats">
                  TXT, MARKDOWN, MDX, PDF, HTML, XLSX, XLS, YTF, PROPERTIES, DOC, DOCX, CSV, EML, MSG, PPTX, XML, EPUB, PPT, MD, HTMをサポートしています。1つあたりの最大サイズは10MBです。
                </p>
                <input
                  type="file"
                  id="fileInput"
                  className="datasetCreateFileInput"
                  multiple
                  onChange={handleFileSelect}
                />
                <label htmlFor="fileInput" className="datasetCreateBrowseButton">
                  参照
                </label>
              </div>
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="datasetCreateActions">
            <div className="datasetCreateCheckboxContainer">
              <input
                type="checkbox"
                id="createEmpty"
                checked={createEmpty}
                onChange={() => setCreateEmpty(!createEmpty)}
                className="datasetCreateCheckbox"
              />
              <label htmlFor="createEmpty" className="datasetCreateCheckboxLabel">
                空のナレッジベースを作成します
              </label>
            </div>
            <button className="datasetCreateNextButton" onClick={handleNext}>
              次へ
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatasetCreate;
