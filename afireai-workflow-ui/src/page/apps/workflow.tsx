import React, { useCallback, useState, useEffect, useRef } from 'react';

import {
  ReactFlow,
  MiniMap,
  Background,
  useNodesState,
  useEdgesState,
  applyNodeChanges,
  applyEdgeChanges,
  addEdge,
  Panel,
  ReactFlowInstance,
  Node,
  Edge
} from '@xyflow/react';

import Header from '../../components/header.tsx';
import Toolbar from '../../components/tools.tsx';
import AddNode from '../../components/addNode.tsx';
import StartNode from '../../nodes/StartNode.tsx';
import EndNode from '../../nodes/EndNode.tsx';
import LLMNode from '../../nodes/LLMNode.tsx';
import DatasetGrepNode from '../../nodes/DatasetGrepNode.tsx';
import CodeNode from '../../nodes/CodeNode.tsx';
import HttpRequestNode from '../../nodes/HttpRequestNode.tsx';
import StartNodeDetailPanel from '../../nodes/StartNodeDetailPanel.tsx';
import EndNodeDetailPanel from '../../nodes/EndNodeDetailPanel.tsx';
import LLMNodeDetailPanel from '../../nodes/LLMNodeDetailPanel.tsx';
import DatasetGrepNodeDetailPanel from '../../nodes/DatasetGrepNodeDetailPanel.tsx';
import CodeNodeDetailPanel from '../../nodes/CodeNodeDetailPanel.tsx';
import HttpRequestNodeDetailPanel from '../../nodes/HttpRequestNodeDetailPanel.tsx';
import { NodeEventProvider, useNodeEvents } from '../../utils/NodeEventContext';
import { NodeExecutionProvider } from '../../utils/NodeExecutionContext';
import { AppResponse } from '../../services/appService';
import { showInfo, showSuccess, showError } from '../../utils/toast';
import { useUIWorkflowController } from '../../hooks/useUIWorkflowController';
import { NodeExecutionStatus } from '../../utils/UIWorkflowController';
import { getAccessToken } from '../../services/authService';
import { debugAuth } from '../../utils/authDebug';
import './workflow-status.css';

import '@xyflow/react/dist/style.css';
import './css/Workflow.css';

const initBgColor = '#f5f5f5';

// 定义节点类型
const nodeTypes = {
  startNode: StartNode,
  endNode: EndNode,
  llmNode: LLMNode,
  datasetGrepNode: DatasetGrepNode,
  codeNode: CodeNode,
  httpRequestNode: HttpRequestNode,
};

// 定义组件属性类型
interface WorkflowProps {
  appInfo: AppResponse | null;
}

// 内部组件，用于处理节点详情面板
const WorkflowContent: React.FC<WorkflowProps> = ({ appInfo }) => {
  const flowKey = 'example-flow';
  const getNodeId = () => `randomnode_${+new Date()}`;

  const [rfInstance, setRfInstance] = useState<ReactFlowInstance | null>(null);

  // 空的初始节点和边
  const initialNodes: Node[] = [];
  const initialEdges: Edge[] = [];

  const [nodes, setNodes] = useNodesState(initialNodes);
  const [edges, setEdges] = useEdgesState(initialEdges);
  const [bgColor] = useState(initBgColor);

  // 使用useRef来跟踪是否已经显示过恢复成功的消息
  const hasShownRestoreMessage = useRef<{[key: string]: boolean}>({});

  // 保存待恢复的viewport信息
  const pendingViewport = useRef<{x: number, y: number, zoom: number} | null>(null);

  // 稳定的回调函数，避免重复初始化控制器
  const handleNodeStateChange = useCallback((nodeId: string, state: any) => {
    console.log(`Node ${nodeId} state changed to ${state.status}`, state);
    // 更新节点视觉状态，但保持位置不变
    setNodes(currentNodes => {
      return currentNodes.map(node => {
        if (node.id === nodeId) {
          const backgroundColor = getNodeBackgroundColor(state.status);
          const className = `node-${state.status.toLowerCase()}`;

          // 记录位置信息用于调试
          console.log(`Node ${nodeId} position before update:`, node.position);

          const updatedNode = {
            ...node,
            data: {
              ...node.data,
              status: state.status
            },
            style: {
              ...node.style,
              backgroundColor: `${backgroundColor} !important`
            },
            className: `${node.className || ''} ${className}`.trim()
          };

          return updatedNode;
        }
        return node;
      });
    });
  }, [setNodes]);

  // 保存Header的setIsRunning函数引用
  const headerSetIsRunning = useRef<((value: boolean) => void) | null>(null);

  const handleWorkflowComplete = useCallback((success: boolean, results: any) => {
    console.log('Workflow completed:', success ? 'SUCCESS' : 'FAILED', results);

    // 关闭Header的loading状态
    if (headerSetIsRunning.current) {
      console.log('Closing header loading state');
      headerSetIsRunning.current(false);
    }

    if (success) {
      showSuccess('ワークフローが正常に完了しました');
    } else {
      showError('ワークフローが失敗しました');
    }
  }, []);

  // 接收Header的setIsRunning函数
  const handleSetIsRunning = useCallback((setIsRunning: (value: boolean) => void) => {
    console.log('Received setIsRunning function from Header');
    headerSetIsRunning.current = setIsRunning;
  }, []);

  // 使用新的UI侧工作流控制器
  const workflowController = useUIWorkflowController({
    apiBaseUrl: 'http://localhost:8000',
    onNodeStateChange: handleNodeStateChange,
    onWorkflowComplete: handleWorkflowComplete
  });

  // 单个节点执行函数
  const executeSingleNode = useCallback(async (nodeId: string) => {
    console.log('开始执行单个节点:', nodeId);

    try {
      // 检查节点是否可以执行
      const canExecute = workflowController.canExecuteSingleNode(nodeId, nodes, edges);
      if (!canExecute.canExecute) {
        showInfo(`无法执行节点: ${canExecute.reason}`);
        return;
      }

      // 获取app_id
      const app_id = appInfo?.app_id;
      if (!app_id) {
        throw new Error('App ID not found');
      }

      // 生成flow_json
      const flow_json = JSON.stringify({
        nodes,
        edges,
        viewport: rfInstance?.getViewport() || { x: 0, y: 0, zoom: 1 }
      });

      // 执行单个节点
      await workflowController.executeSingleNode(nodeId, nodes, edges, app_id, flow_json);

      console.log(`节点 ${nodeId} 执行完成`);

    } catch (error) {
      console.error('单个节点执行失败:', error);
      showInfo(`节点执行失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }, [nodes, edges, workflowController, rfInstance, appInfo?.app_id]);

  // 使用节点事件上下文
  const { selectedNode, clearSelectedNode } = useNodeEvents();

  // 添加调试信息
  useEffect(() => {
    console.log('Current selected node (WorkflowContent):', selectedNode);
  }, [selectedNode]);

  // 组件初始化时重置工作流执行状态
  useEffect(() => {
    console.log('WorkflowContent component initialized, resetting workflow execution state');
    // 重置工作流控制器状态
    workflowController.resetState();
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 处理flow_json恢复
  useEffect(() => {
    if (appInfo && appInfo.flow_json && appInfo.flow_json.trim() !== '') {
      try {
        // 检查是否已经为当前应用ID显示过恢复成功的消息
        const appId = appInfo.app_id;
        if (hasShownRestoreMessage.current[appId]) {
          console.log(`Restore message already shown for app_id: ${appId}, skipping...`);
          return;
        }

        console.log('Restoring flow from flow_json:', appInfo.flow_json);

        // 尝试解析flow_json
        const flowData = JSON.parse(appInfo.flow_json);
        console.log('Flow data structure:', Object.keys(flowData));
        console.log('Viewport in flow_json:', flowData.viewport);

        if (flowData.nodes && flowData.edges) {
          // 恢复节点和边，但重置所有节点为初始状态
          const resetNodes = flowData.nodes.map((node: any) => {
            // 清除所有执行状态相关的样式和数据
            const cleanedNode = {
              ...node,
              data: {
                ...node.data,
                status: undefined // 清除状态
              },
              style: {
                ...node.style,
                backgroundColor: undefined, // 清除背景色
                border: undefined // 清除边框
              },
              className: node.className ?
                node.className.replace(/\s*node-(pending|running|success|failed)\s*/g, '').trim() :
                node.className // 保留原始类名，不设置为undefined
            };

            console.log(`重置节点 ${node.id} 为初始状态`);
            return cleanedNode;
          });

          setNodes(resetNodes);
          setEdges(flowData.edges);

          // 恢复viewport（缩放和位置）
          if (flowData.viewport) {
            const { x = 0, y = 0, zoom = 1 } = flowData.viewport;
            console.log(`Restoring viewport: x=${x}, y=${y}, zoom=${zoom}`);

            // 保存viewport信息，等待rfInstance可用时恢复
            pendingViewport.current = { x, y, zoom };
          }

          // 显示恢复成功的消息，并标记为已显示
          // showInfo('ワークフローが正常に復元されました');
          hasShownRestoreMessage.current[appId] = true;

          console.log('Flow restored with nodes reset to initial state and viewport restored');
        }
      } catch (error) {
        console.error('Failed to parse flow_json:', error);
      }
    }
  }, [appInfo, setNodes, setEdges, rfInstance]);

  // 当rfInstance可用时，恢复待处理的viewport
  useEffect(() => {
    if (rfInstance && pendingViewport.current) {
      const { x, y, zoom } = pendingViewport.current;
      console.log(`Restoring pending viewport: x=${x}, y=${y}, zoom=${zoom}`);
      rfInstance.setViewport({ x, y, zoom });
      pendingViewport.current = null; // 清除待处理的viewport
    }
  }, [rfInstance]);

  const onNodesChange = useCallback(
    (changes: any) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [],
  );

  const onEdgesChange = useCallback(
    (changes: any) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [],
  );

  const onConnect = useCallback(
    (params: any) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  // 获取节点背景颜色
  const getNodeBackgroundColor = (status: string | NodeExecutionStatus): string => {
    switch (status) {
      case 'RUNNING':
        return '#e8f5e8'; // 浅绿色
      case 'SUCCESS':
        return '#e3f2fd'; // 浅蓝色
      case 'FAILED':
        return '#ffebee'; // 浅红色
      case 'PENDING':
      case 'WAITING_MANUAL':
      default:
        return '#ffffff'; // 白色
    }
  };

  // 重置所有节点状态为初始状态
  const resetAllNodesStatus = useCallback(() => {
    console.log('重置所有节点状态为初始状态');
    setNodes(currentNodes => {
      const updatedNodes = currentNodes.map(node => {
        console.log(`重置节点 ${node.id} 为初始状态`);

        // 完全清除所有执行状态相关的样式和数据，就像页面刷新时一样
        const cleanedNode = {
          ...node,
          data: {
            ...node.data,
            status: undefined // 清除状态
          },
          style: {
            ...node.style,
            backgroundColor: undefined, // 清除背景色
            border: undefined // 清除边框
          },
          className: node.className ?
            node.className.replace(/\s*node-(pending|running|success|failed)\s*/g, '').trim() :
            node.className // 保留原始类名，不设置为undefined
        };

        return cleanedNode;
      });

      console.log('所有节点已重置为初始状态:', updatedNodes.map(n => ({
        id: n.id,
        status: n.data?.status,
        backgroundColor: n.style?.backgroundColor,
        className: n.className
      })));

      return updatedNodes;
    });
  }, [setNodes]);

  // 新的工作流执行函数
  const executeWorkflow = useCallback(async () => {
    console.log('开始执行工作流');

    try {
      // 调试认证状态
      const authDebugInfo = debugAuth();
      console.log('认证调试信息:', authDebugInfo);

      if (!authDebugInfo.hasToken) {
        throw new Error('No authentication token available');
      }

      // 重置所有节点状态
      resetAllNodesStatus();

      // 生成flow_json
      const flow_json = JSON.stringify({
        nodes,
        edges,
        viewport: rfInstance?.getViewport() || { x: 0, y: 0, zoom: 1 }
      });

      // 获取app_id
      const app_id = appInfo?.app_id;
      if (!app_id) {
        throw new Error('App ID not found');
      }

      // 使用新的UI侧控制器执行工作流
      await workflowController.executeWorkflow(nodes, edges, app_id, flow_json);

    } catch (error) {
      console.error('Failed to execute workflow:', error);
      showInfo('ワークフローの実行に失敗しました');
    }
  }, [nodes, edges, workflowController, resetAllNodesStatus, rfInstance, appInfo?.app_id]);



  return (
    <NodeExecutionProvider executeSingleNode={executeSingleNode}>
      <div className="workflow-container">
        <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        className="react-flow"
        onInit={setRfInstance}
        style={{ backgroundColor: bgColor }}
        fitView={false}
        fitViewOptions={{ padding: 0.1 }}
        preventScrolling={false}
        nodesDraggable={true}
        nodesConnectable={true}
        elementsSelectable={true}
      >
        <Background />
        <Panel position="bottom-right" style={{ bottom: '30px' }}>
          <MiniMap />
        </Panel>

        <Panel position="bottom-left" style={{ bottom: '32px' }}>
          <Toolbar />
        </Panel>
        <Panel position="bottom-center" style={{ bottom: '45px' }}>
          <AddNode />
        </Panel>
        <Panel position="top-right">
          <Header
            rfInstance={rfInstance}
            flowKey={flowKey}
            setNodes={setNodes}
            setEdges={setEdges}
            getNodeId={getNodeId}
            onWorkflowRun={executeWorkflow}
            onSetIsRunning={handleSetIsRunning}
          />
        </Panel>
      </ReactFlow>

      {/* 显示节点详情面板 */}
      {selectedNode && (
        <div>
          {selectedNode.type === 'startNode' && (
            <StartNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'endNode' && (
            <EndNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'llmNode' && (
            <LLMNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'datasetGrepNode' && (
            <DatasetGrepNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'codeNode' && (
            <CodeNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'httpRequestNode' && (
            <HttpRequestNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
        </div>
      )}
      </div>
    </NodeExecutionProvider>
  );
};

// 主组件，提供上下文
const Workflow: React.FC<WorkflowProps> = ({ appInfo }) => {
  return (
    <NodeEventProvider>
      <WorkflowContent appInfo={appInfo} />
    </NodeEventProvider>
  );
};

export default Workflow;
