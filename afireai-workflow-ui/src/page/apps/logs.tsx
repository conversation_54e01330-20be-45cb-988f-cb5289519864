import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams } from 'react-router-dom';
import './css/logs.css';
import { FaRobot, FaGlobe, FaFolder, FaStop, FaSearch, FaCaretDown, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { GrPlay } from 'react-icons/gr';

import { BsCalendar3 } from 'react-icons/bs';
import {
  getWorkflowLogs,
  LogEntry,
  LogsResponse,
  LogsFilters,
  calculateRuntime,
  formatDateTime,
  getStatusDisplayName,
  getStatusClassName,
  getTimeRangeDisplayName,
  getStatusDisplayNameJa,
  getWorkflowRunNodeLogs, // 新增导入
  WorkflowRunNodeLogEntry // 新增导入
} from '../../services/logsService';
import { showError, showInfo } from '../../utils/toast';

// 定義ノード名称マッピング
const nodeNameMap: { [key: string]: string } = {
  'start': '開始',
  'end': '終了',
  'llm': 'LLM',
  'code': 'コード実行',
  'http_request': 'HTTPリクエスト',
  'dataset_grep': 'データセット検索'
  // 根据需要添加更多マッピング
};

const getNodeDisplayName = (nodeId: string): string => {
  // 尝試からマッピング中から名称を取得
  const mappedName = nodeNameMap[nodeId.toLowerCase()];
  if (mappedName) {
    return mappedName;
  }
  // マッピング中にない場合は、元のIDや他の処理を返す
  return nodeId;
};

// 1. ポップアップ位置調整
// 2. ノードタイプ名称マッピング
const nodeTypeNameMap: { [key: string]: string } = {
  startNode: '開始',
  llmNode: 'LLM',
  datasetGrepNode: 'ナレッジ検索', // 更新为画像中の名称
  codeNode: 'コード実行',
  httpRequestNode: 'HTTPリクエスト',
  endNode: '終了',
};

// 更新获取ノードタイプの関数
function getNodeTypeDisplayName(type: string) {
  return nodeTypeNameMap[type] || type;
}

function getNodeTypeFromId(nodeId: string) {
  if (!nodeId) return '';
  if (nodeId.startsWith('start')) return 'startNode';
  if (nodeId.startsWith('llm')) return 'llmNode';
  if (nodeId.startsWith('dataset-grep')) return 'datasetGrepNode';
  if (nodeId.startsWith('code')) return 'codeNode';
  if (nodeId.startsWith('http-request')) return 'httpRequestNode';
  if (nodeId.startsWith('end')) return 'endNode';
  return nodeId;
}

function getNodeTypeIconClass(type: string) {
  switch (type) {
    case 'startNode': return 'start-node-icon';
    case 'llmNode': return 'llm-node-icon';
    case 'datasetGrepNode': return 'dataset-grep-node-icon';
    case 'codeNode': return 'code-node-icon';
    case 'httpRequestNode': return 'http-request-node-icon';
    case 'endNode': return 'end-node-icon';
    default: return 'node-icon-default';
  }
}

// 修改後の获取ノードタイプアイコンの関数
function getNodeTypeIcon(type: string) {
  switch (type) {
    case 'startNode': return <GrPlay />;
    
    case 'llmNode': return <FaRobot />;
    case 'datasetGrepNode': return <FaFolder />;
    case 'codeNode': return <FaRobot />;
    case 'httpRequestNode': return <FaGlobe />;
    case 'endNode': return <FaStop />;
    default: return <GrPlay />;
  }
}

// 更新获取ノードステータスアイコンの関数
function getNodeStatusIcon(status: string) {
  if (status === 'FAILED') return { icon: 'cancel', className: 'failed' };
  if (status === 'RUNNING') return { icon: 'hourglass_empty', className: 'running' };
  return { icon: 'check_circle', className: 'success' };
}

function getNodeDurationSec(started_at: string, finished_at: string) {
  if (!started_at || !finished_at) return '-';
  const s = new Date(started_at).getTime();
  const e = new Date(finished_at).getTime();
  if (isNaN(s) || isNaN(e)) return '-';
  const sec = (e - s) / 1000;
  return sec > 0 ? sec.toFixed(2) + 's' : '0.00s';
}

const Logs = () => {
  // URLからapp_idを取得
  const { id: appId } = useParams<{ id: string }>();

  // ドロップダウンコンテナのRefs
  const statusDropdownRef = useRef<HTMLDivElement>(null);
  const timeDropdownRef = useRef<HTMLDivElement>(null);

  // 状態管理
  const [allLogs, setAllLogs] = useState<LogEntry[]>([]); // 全ての原始データを保存
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]); // フィルタリングされたデータを保存
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [timeFilter, setTimeFilter] = useState('7days');
  const [selectedStatus, setSelectedStatus] = useState<string>('ALL');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showTimeDropdown, setShowTimeDropdown] = useState(false);
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null); // 新增状態：選択されたログエントリ
  const [nodeLogs, setNodeLogs] = useState<WorkflowRunNodeLogEntry[]>([]); // 新增状態：ノードログデータ
  const [nodeLogsLoading, setNodeLogsLoading] = useState(false); // 新增状態：ノードログロード状態
  const [activeTab, setActiveTab] = useState<'details' | 'trace'>('details'); // 新增状態：現在アクティブなタブ

  const pageSize = 30;

  // 状態オプション
  const statusOptions = [
    { value: 'ALL', label: 'All' },
    { value: 'SUCCESS', label: 'Success' },
    { value: 'FAILED', label: 'Failed' },
    { value: 'RUNNING', label: 'Running' }
  ];

  const timeOptions = [
    { value: 'today', label: '今日' },
    { value: '7days', label: '過去7日間' },
    { value: '1month', label: '過去1ヶ月' },
    { value: '6months', label: '過去6ヶ月' },
    { value: 'all', label: 'すべての期間' }
  ];

  // API呼び出し関数 - ページごとに全てのデータを取得
  const fetchLogs = useCallback(async () => {
    if (!appId) {
      showError('アプリIDが見つかりません');
      return;
    }

    setLoading(true);
    try {
      let allData: LogEntry[] = [];
      let currentPage = 1;
      let hasMoreData = true;
      const maxPageSize = 100; // API制限の最大ページサイズ

      // ページごとに全てのデータを取得
      while (hasMoreData) {
        const filters: LogsFilters = {
          app_id: appId,
          status: selectedStatus,
          time_range: timeFilter,
          page: currentPage,
          page_size: maxPageSize
        };

        const response = await getWorkflowLogs(filters);
        allData = [...allData, ...response.logs];

        // さらにデータがあるかどうかをチェック
        hasMoreData = response.logs.length === maxPageSize && currentPage < response.pagination.total_pages;
        currentPage++;

        // セーフティチェック、無限ループを避ける
        if (currentPage > 50) { // 最大50ページのデータを取得
          console.warn('最大ページ数制限に達しました、データ取得を停止します');
          break;
        }
      }

      setAllLogs(allData);
    } catch (error) {
      console.error('Failed to fetch logs:', error);
      showError('ログの取得に失敗しました');
      setAllLogs([]);
    } finally {
      setLoading(false);
    }
  }, [appId, selectedStatus, timeFilter]);

  // フロントエンドフィルタリングロジック
  const filterLogs = useCallback(() => {
    let filtered = [...allLogs];

    // 検索フィルタリング
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(log =>
        log.workflow_run_id.toLowerCase().includes(searchLower) ||
        log.app_id.toLowerCase().includes(searchLower) ||
        log.user_id.toLowerCase().includes(searchLower) ||
        (log.params && log.params.toLowerCase().includes(searchLower)) ||
        (log.memory && log.memory.toLowerCase().includes(searchLower))
      );
    }

    setFilteredLogs(filtered);

    // ページング情報を計算
    const totalCount = filtered.length;
    const totalPages = Math.ceil(totalCount / pageSize);
    setTotalCount(totalCount);
    setTotalPages(totalPages);

    // 現在のページが範囲外の場合、最初のページにリセット
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1);
    }
  }, [allLogs, searchTerm, pageSize, currentPage]);

  // 初期ロードと依存関係が変化したときに再読み込み
  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  // データや検索条件が変化したときに再フィルタリング
  useEffect(() => {
    filterLogs();
  }, [filterLogs]);

  // 外部領域をクリックしてドロップダウンメニューを閉じる
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // ステータスドロップダウンメニューをチェック
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target as Node)) {
        setShowStatusDropdown(false);
      }

      // 時間ドロップダウンメニューをチェック
      if (timeDropdownRef.current && !timeDropdownRef.current.contains(event.target as Node)) {
        setShowTimeDropdown(false);
      }
    };

    // イベントリスナーを追加
    document.addEventListener('mousedown', handleClickOutside);

    // クリーンアップ関数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 検索処理
  const handleSearch = useCallback((value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // 最初のページにリセット
  }, []);

  // ステータスフィルタリング処理
  const handleStatusChange = useCallback((status: string) => {
    setSelectedStatus(status);
    setCurrentPage(1);
    setShowStatusDropdown(false);
  }, []);

  // 時間フィルタリング処理
  const handleTimeChange = useCallback((timeRange: string) => {
    setTimeFilter(timeRange);
    setCurrentPage(1);
    setShowTimeDropdown(false);
  }, []);

  // ページング処理
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // 選択されたステータスの表示名を取得
  const getSelectedStatusLabel = () => {
    return statusOptions.find(option => option.value === selectedStatus)?.label || 'すべて';
  };

  // 選択された時間範囲の表示名を取得
  const getSelectedTimeLabel = () => {
    return timeOptions.find(option => option.value === timeFilter)?.label || '過去7日間';
  };

  // 現在のページのデータを取得
  const getCurrentPageLogs = useCallback(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredLogs.slice(startIndex, endIndex);
  }, [filteredLogs, currentPage, pageSize]);

  console.log("--------------------------------");
  console.log(nodeLogs);
  console.log("--------------------------------");
  // ログ行クリックイベントの処理
  const handleLogRowClick = useCallback(async (log: LogEntry) => {
    setSelectedLog(log);
    setActiveTab('trace'); // 実行追跡をデフォルト表示
    if (log.workflow_run_id) {
      setNodeLogsLoading(true);
      try {
        const nodes = await getWorkflowRunNodeLogs(log.workflow_run_id);
        // started_at昇順でノードログをソート
        const sortedNodes = nodes.sort((a, b) => new Date(a.started_at).getTime() - new Date(b.started_at).getTime());
        setNodeLogs(sortedNodes);
      } catch (error) {
        showError('ノードログの取得に失敗しました');
        setNodeLogs([]);
      } finally {
        setNodeLogsLoading(false);
      }
    } else {
      setNodeLogs([]);
    }
  }, []);

  return (
    <div className="logs-container">
      <div className="logs-header">
        <h1>ワークフローログ</h1>
        <p>このログは Automate の操作を記録しました。</p>
      </div>

      <div className="logs-filters">
        <div className="filter-group">
          {/* ステータスフィルタリングドロップダウンメニュー */}
          <div className="filter-dropdown" ref={statusDropdownRef}>
            <button
              className="filter-button"
              onClick={() => setShowStatusDropdown(!showStatusDropdown)}
            >
              <span>{getSelectedStatusLabel()}</span>
              <FaCaretDown className="filter-icon" />
            </button>
            {showStatusDropdown && (
              <div className="dropdown-menu">
                {statusOptions.map(option => (
                  <div
                    key={option.value}
                    className={`dropdown-item ${selectedStatus === option.value ? 'active' : ''}`}
                    onClick={() => handleStatusChange(option.value)}
                  >
                    <span>{option.label}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 時間フィルタリングドロップダウンメニュー */}
          <div className="filter-dropdown" ref={timeDropdownRef}>
            <button
              className="time-filter-button"
              onClick={() => setShowTimeDropdown(!showTimeDropdown)}
            >
              <BsCalendar3 className="filter-icon" />
              <span>{getSelectedTimeLabel()}</span>
              {/* <FaChevronDown className="filter-icon" /> */}
            </button>
            {showTimeDropdown && (
              <div className="dropdown-menu">
                {timeOptions.map(option => (
                  <div
                    key={option.value}
                    className={`dropdown-item ${timeFilter === option.value ? 'active' : ''}`}
                    onClick={() => handleTimeChange(option.value)}
                  >
                    {option.label}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 検索ボックス */}
          <div className="search-container">
            <FaSearch className="search-icon" />
            <input
              type="text"
              className="search-input"
              placeholder="検索"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="logs-table-container">
        <table className="logs-table">
          <thead>
            <tr>
              <th className="column-timestamp">開始時間</th>
              <th className="column-status">ステータス</th>
              <th className="column-runtime">ランタイム</th>
              <th className="column-tokens">トークン</th>
              <th className="column-user">エンドユーザーまたはアカウント</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={5} className="loading">
                  読み込み中...
                </td>
              </tr>
            ) : (() => {
                const currentPageLogs = getCurrentPageLogs();
                return currentPageLogs.length > 0 ? (
                  currentPageLogs.map((log: LogEntry) => (
                    <tr key={log.workflow_run_id} className="log-row" onClick={() => handleLogRowClick(log)} style={{ cursor: 'pointer' }}>
                      <td className="column-timestamp">
                        <span className="dot"></span>
                        {formatDateTime(log.started_at)}
                      </td>
                      <td className="column-status">
                        <span className={`status-badge ${getStatusClassName(log.status)}`}> 
                          {getStatusDisplayName(log.status)}
                        </span>
                      </td>
                      <td className="column-runtime">
                        {calculateRuntime(log.started_at, log.finished_at)}
                      </td>
                      <td className="column-tokens">-</td>
                      <td className="column-user">{log.workflow_run_id}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="no-logs">
                      {searchTerm ? 'ログが見つかりませんでした。検索条件を変更してください。' : 'ログがありません。'}
                    </td>
                  </tr>
                );
              })()}
          </tbody>
        </table>
      </div>

      {/* ページングコンポーネント */}
      {totalPages > 1 && (
        <div className="pagination">
          <div className="pagination-info">
            {totalCount}件中 {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, totalCount)}件を表示
          </div>
          <div className="pagination-controls">
            <button
              className="pagination-button"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <FaChevronLeft />
            </button>

            {/* ページ番号ボタン */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum: number;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  className={`pagination-button ${currentPage === pageNum ? 'active' : ''}`}
                  onClick={() => handlePageChange(pageNum)}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              className="pagination-button"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              <FaChevronRight />
            </button>
          </div>
        </div>
      )}

      {/* 詳細情報ポップアップ/サイドバー */} 
      {selectedLog && (
        <div 
          className="log-detail-modal-overlay" 
          onClick={() => setSelectedLog(null)} // 新增点击overlay关闭功能
        >
          <div 
            className="log-detail-modal log-detail-modal-right"
            onClick={(e) => e.stopPropagation()} // 阻止クリックイベント冒泡
          >
            <div className="log-detail-header">
              <h2 className="log-detail-title">ログの詳細</h2>
              <button onClick={() => setSelectedLog(null)} className="close-button">×</button>
            </div>
            <div className="log-detail-tabs">
              <button 
                className={`tab-button ${activeTab === 'details' ? 'active' : ''}`}
                onClick={() => setActiveTab('details')}
              >
                結果
              </button>
              <button 
                className={`tab-button ${activeTab === 'trace' ? 'active' : ''}`}
                onClick={() => setActiveTab('trace')}
              >
                実行追跡
              </button>
            </div>
            <div className="log-detail-content">
              {activeTab === 'details' && (
                <div>
                  <p><strong>Workflow Run ID:</strong> {selectedLog.workflow_run_id}</p>
                  <p><strong>Status:</strong> {getStatusDisplayName(selectedLog.status)}</p>
                  <p><strong>Started At:</strong> {formatDateTime(selectedLog.started_at)}</p>
                  <p><strong>Finished At:</strong> {selectedLog.finished_at ? formatDateTime(selectedLog.finished_at) : '-'}</p>
                  <p><strong>Runtime:</strong> {calculateRuntime(selectedLog.started_at, selectedLog.finished_at)}</p>
                  <p><strong>Params:</strong> {selectedLog.params || '-'}</p>
                  <p><strong>Memory:</strong> {selectedLog.memory || '-'}</p>
                </div>
              )}
              {activeTab === 'trace' && (
                <div className="trace-container">
                  {nodeLogsLoading ? (
                    <p>ノードログを読み込み中...</p>
                  ) : nodeLogs.length > 0 ? (
                    <ul className="node-log-list">
                      {nodeLogs.map(nodeLog => {
                        const type = getNodeTypeFromId(nodeLog.node_id || '');
                        const iconClass = getNodeTypeIconClass(type);
                        const icon = getNodeTypeIcon(type);
                        const nodeName = getNodeTypeDisplayName(type);
                        const { icon: statusIcon, className: statusClass } = getNodeStatusIcon(nodeLog.status || '');
                        return (
                          <li key={nodeLog.id} className="node-log-item">
                            <div className="node-log-item-header">
                              <span className={`my-node-icon ${iconClass}`}>{icon}</span>
                              <span className="node-name">{nodeName}</span>
                              <div className="node-right">
                                <span className="node-runtime">{getNodeDurationSec(nodeLog.started_at || '', nodeLog.finished_at || '')}</span>
                                <span className={`my-node-status-indicator ${statusClass}`}><span className="material-icons">{statusIcon}</span></span>
                              </div>
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  ) : (
                    <p>このワークフロー実行のノードログはありません。</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Logs;
