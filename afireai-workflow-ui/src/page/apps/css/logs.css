@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

/* 日志ページスタイル */

.logs-container {
    width: 100%;
    max-width: 1200px;
    margin: 0;
    padding: 20px;
    color: #333;
    background-color: #fff;
    overflow-y: auto;
    height: 100%;
}

.logs-header {
    margin-bottom: 20px;
    position: relative;
}

.logs-header h1 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.logs-header p {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

.logs-filters {
    display: flex;
    margin-bottom: 15px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.filter-button {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    position: relative;
    height: 38px;
}

.filter-icon {
    font-size: 16px;
}

.status-indicator {
    font-size: 10px;
    margin-left: 8px;
}

.status-indicator.success {
    color: #22c55e;
}

.status-indicator.error {
    color: #ef4444;
}

.time-filter-button {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    height: 38px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    width: 400px;
    height: 38px;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: #999;
    font-size: 16px;
    z-index: 1;
    pointer-events: none;
}

.search-input {
    border: none;
    background: transparent;
    padding: 0 12px 0 36px;
    width: 100%;
    font-size: 13px;
    color: #333;
    outline: none;
    height: 100%;
}

.logs-table-container {
    width: 100%;
    overflow-x: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
}

.logs-table thead {
    background-color: #f5f5f5;
}

.logs-table th {
    padding: 10px 15px;
    text-align: left;
    font-weight: 500;
    font-size: 13px;
    color: #666;
    border-bottom: 1px solid #e0e0e0;
}

.logs-table td {
    padding: 10px 15px;
    font-size: 13px;
    border-bottom: 1px solid #e0e0e0;
}

.logs-table tr:last-child td {
    border-bottom: none;
}

.column-timestamp {
    width: 160px;
    position: relative;
}

.column-status {
    width: 100px;
}

.column-runtime {
    width: 100px;
}

.column-tokens {
    width: 110px;
}

.column-user {
    min-width: 260px;
}

.dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #4299e1;
    border-radius: 50%;
    margin-right: 10px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.success {
    background-color: #e6f7e6;
    color: #22c55e;
}

.status-badge.error {
    background-color: #fee2e2;
    color: #ef4444;
}

.no-logs {
    padding: 30px;
    text-align: center;
    color: #999;
}

.loading {
    padding: 30px;
    text-align: center;
    color: #666;
}


/* 下拉メニュースタイル */

.filter-dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 150px;
    margin-top: 4px;
}

.dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    transition: background-color 0.2s;
    font-size: 13px;
}

.dropdown-item:hover {
    background-color: #f5f5f5;
}

.dropdown-item.active {
    background-color: #e6f7ff;
    color: #1890ff;
}

.dropdown-item:first-child {
    border-radius: 8px 8px 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 8px 8px;
}


/* 状態指示器スタイル */

.status-indicator.failed {
    color: #ef4444;
}

.status-indicator.running {
    color: #f59e0b;
}

.status-indicator.unknown {
    color: #6b7280;
}


/* 状態徽章スタイル */

.status-badge.failed {
    background-color: #fee2e2;
    color: #ef4444;
}

.status-badge.running {
    background-color: #fef3c7;
    color: #f59e0b;
}

.status-badge.unknown {
    background-color: #f3f4f6;
    color: #6b7280;
}


/* 分ページスタイル */

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 0 4px;
}

.pagination-info {
    font-size: 13px;
    color: #666;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 4px;
}

.pagination-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #e0e0e0;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    color: #333;
    transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
}

.pagination-button.active {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
}

.pagination-button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background-color: #f9f9f9;
}


/* Add more specific styles as needed */


/* Log Detail Modal Styles */

.log-detail-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.log-detail-modal {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 800px;
    /* Increased max-width for better layout */
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    /* Prevent content overflow from breaking layout */
}

.log-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
}

.log-detail-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #757575;
}

.log-detail-tabs {
    display: flex;
    padding: 0 24px;
    border-bottom: 1px solid #e0e0e0;
}

.tab-button {
    background: none;
    border: none;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 0.9rem;
    /* Slightly reduced font size */
    color: #757575;
    border-bottom: 2px solid transparent;
    margin-right: 16px;
    /* Added margin for spacing */
}

.tab-button.active {
    color: #1976d2;
    /* Or your primary color */
    border-bottom-color: #1976d2;
    /* Or your primary color */
    font-weight: 600;
}

.log-detail-content {
    padding: 15px;
    overflow-y: auto;
    flex-grow: 1;
    min-height: 240px;
}

.log-detail-content p {
    margin-bottom: 10px;
    line-height: 1.4;
}

.log-detail-content strong {
    font-weight: 600;
    margin-right: 8px;
}

/* 調整ログ詳細タイトルフォントサイズ */
.log-detail-title {
  font-size: 18px; /* 根据需要调整 */
}

/* 調整アイコンサイズ */
.my-node-icon .material-icons,
.my-node-status-indicator .material-icons {
  font-size: 16px; /* 根据需要調整 */
}


/* Trace Container Styles */

.trace-container {
    /* Styles for the execution trace view */
}

.node-log-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.node-log-item {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.node-log-item:last-child {
    border-bottom: none;
}


/* 1. 让整个头部横向布局 */

.node-log-item-header {
    display: flex;
    align-items: center;
}

.node-right {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.node-icon {
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.node-icon .material-icons {
    font-size: 20px;
    /* Adjust icon size as needed */
    color: #5f6368;
    /* Default icon color */
}


/* Specific icon colors if needed */

.node-icon-start .material-icons {
    color: #1976d2;
}


/* Blue for start */

.node-icon-code .material-icons {
    color: #388e3c;
}


/* Green for code */

.node-icon-end .material-icons {
    color: #f57c00;
}


/* Orange for end */

.node-name {
    font-weight: 500;
    color: #333;
    margin-right: 8px;
}

.node-runtime {
    /* margin-right: 8px;
    color: #757575;
    font-size: 0.85rem; */
    margin-right: 0.8rem;
}

.my-node-status-indicator {
    position: relative;
}

.my-node-status-indicator .material-icons {
    font-size: 18px;
    /* Adjust status icon size */
}

.my-node-status-indicator.success .material-icons {
    color: #4caf50;
}


/* Green for success */

.my-node-status-indicator.failed .material-icons {
    color: #f44336;
}


/* Red for failed */

.my-node-status-indicator.running .material-icons {
    color: #ff9800;
}


/* Orange for running */

.node-log-item-details {
    padding-left: 36px;
    /* Indent details */
    margin-top: 8px;
    font-size: 0.85rem;
    color: #555;
}

.node-log-item-details p {
    margin: 4px 0;
}

.logs-table tr.log-row:hover {
    background-color: #f5f5f5;
    cursor: pointer;
}

.log-detail-modal-right {
    position: fixed;
    right: 10px;
    bottom: 10px;
    width: 400px;
    height: 480px;
    max-width: 100vw;
    max-height: 90vh;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
    background: #fff;
    display: flex;
    flex-direction: column;
    z-index: 1100;
    animation: slideInRight 0.3s;
}

@keyframes slideInRight {
    from {
        right: -500px;
        opacity: 0;
    }
    to {
        right: 0;
        opacity: 1;
    }
}

.node-icon-llm .material-icons {
    /* 无背景色 */
}

.node-icon-datasetgrep .material-icons {
    /* 无背景色 */
}

.node-icon-http .material-icons {
    /* 无背景色 */
}

.node-icon-default .material-icons {
    /* 无背景色 */
}

.node-log-item-details {
    padding-left: 36px;
    /* Indent details */
    margin-top: 8px;
    font-size: 0.85rem;
    color: #555;
}

.node-log-item-details p {
    margin: 4px 0;
}