import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import "./css/apps.css";
import { FcFlowChart } from 'react-icons/fc';
import { FiMoreVertical } from 'react-icons/fi';
import CreateAppModal from "../../components/CreateAppModal";
import { createApp, getApps, deleteApp, AppResponse } from "../../services/appService";
import { showSuccess, showError, showInfo } from "../../utils/toast";


// 创建应用卡片类型
interface AppCard {
  id?: string;
  title: string;
  desc: string;
  tag: string;
  icon: string;
  isCreateCard?: boolean;
}

// 初始应用列表
const initialApps: AppCard[] = [];

const Apps = () => {
  const navigate = useNavigate();
  const [search, setSearch] = useState("");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [apps, setApps] = useState<AppCard[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 菜单状态
  const [menuOpen, setMenuOpen] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState<string | null>(null);

  // 使用useRef来跟踪API调用，这样在严格模式下不会重复调用
  const hasCalledApi = useRef(false);

  // 创建"创建应用"卡片
  const createAppCard: AppCard = {
    title: "アプリを作成する",
    desc: "ワークフロー",
    tag: "ワークフロー",
    icon: "",
    isCreateCard: true
  };

  // 刷新应用列表的函数
  const refreshAppList = async () => {
    try {
      setIsLoading(true);
      console.log("Refreshing apps list...");

      try {
        // 调用API获取应用列表
        const appList = await getApps();
        console.log("API response:", appList);

        if (appList && appList.length > 0) {
          // 格式化API返回的数据
          const formattedApps = appList.map(app => ({
            id: app.app_id,
            title: app.app_name,
            desc: app.app_description || "ワークフロー",
            tag: app.tag || "workflow",
            icon: app.app_icon_file || "🤖"
          }));

          console.log("Formatted apps:", formattedApps);
          setApps(formattedApps);
        } else {
          console.log("No apps returned from API");
          setApps([]);
        }
      } catch (apiError) {
        console.error("API call failed:", apiError);
        showError("アプリリストの更新に失敗しました");
      }
    } catch (error) {
      console.error("Failed to refresh apps:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取应用列表
  useEffect(() => {
    const fetchApps = async () => {
      // 检查是否已经调用过API，防止在严格模式下重复调用
      if (hasCalledApi.current) {
        console.log("API already called, skipping...");
        return;
      }

      // 标记为已调用
      hasCalledApi.current = true;

      try {
        setIsLoading(true);
        console.log("Fetching apps from API...");

        try {
          // 调用API获取应用列表
          const appList = await getApps();
          console.log("API response:", appList);

          if (appList && appList.length > 0) {
            // 格式化API返回的数据
            const formattedApps = appList.map(app => ({
              id: app.app_id,
              title: app.app_name,
              desc: app.app_description || "ワークフロー",
              tag: app.tag || "workflow",
              icon: app.app_icon_file || "🤖"
            }));

            console.log("Formatted apps:", formattedApps);
            setApps(formattedApps);

            // 显示成功消息（现在只会显示一次，因为我们已经防止了重复调用）
            // showSuccess("アプリリストを正常に取得しました");
          } else {
            console.log("No apps returned from API, using initial apps");
            setApps(initialApps);
          }
        } catch (apiError) {
          console.error("API call failed:", apiError);

          // 显示错误消息
          showError("アプリリストの取得に失敗しました");

          // 使用初始应用列表作为备用
          setApps(initialApps);
        }
      } catch (error) {
        console.error("Failed to fetch apps:", error);
        setApps(initialApps);
      } finally {
        setIsLoading(false);
      }
    };

    fetchApps();
  }, []);

  // 处理创建应用
  const handleCreateApp = async (appName: string, appDescription: string, appIconFile: string, tag: string) => {
    try {
      setIsLoading(true);
      console.log('Creating app with:', { appName, appDescription, appIconFile, tag });

      // 创建一个变量来存储响应数据
      let responseData: AppResponse;

      // 尝试使用真实API
      try {
        const response = await createApp(appName, appDescription, appIconFile, tag);
        console.log('API response:', response);

        // 使用API响应
        responseData = response;
      } catch (apiError) {
        console.error('API call failed, using mock data:', apiError);

        // 如果API调用失败，使用模拟数据
        responseData = {
          app_id: `app-${Date.now()}`,
          app_name: appName,
          app_description: appDescription,
          app_icon_file: appIconFile,
          tag: tag,
          flow_json: "",
          user_id: "1024",
          ip_addr: "127.0.0.1",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        console.log('Using mock response:', responseData);
      }

      // 添加新创建的应用到列表
      const newApp: AppCard = {
        id: responseData.app_id,
        title: responseData.app_name,
        desc: responseData.app_description || "ワークフロー",
        tag: responseData.tag || "workflow",
        icon: responseData.app_icon_file || "🤖"
      };

      // 更新应用列表
      setApps(prevApps => [...prevApps, newApp]);
      setIsCreateModalOpen(false);

      // 显示成功消息
      showSuccess("アプリが正常に作成されました！");

      // 重新获取应用列表以确保显示最新数据
      refreshAppList();
    } catch (error) {
      console.error("Failed to create app:", error);
      showError(`アプリの作成に失敗しました: ${error instanceof Error ? error.message : '未知のエラー'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理应用卡片点击
  const handleAppCardClick = (app: AppCard) => {
    if (app.isCreateCard) {
      // 点击"创建应用"卡片，打开创建应用弹窗
      setIsCreateModalOpen(true);
    } else if (app.id) {
      // 点击现有应用卡片，导航到工作流页面
      navigate(`/app/workflow/${app.id}`);
    }
  };

  // 处理菜单按钮点击
  const handleMenuClick = (e: React.MouseEvent, appId: string) => {
    e.stopPropagation(); // 阻止事件冒泡，防止触发卡片点击
    setMenuOpen(appId === menuOpen ? null : appId);
  };

  // 处理删除按钮点击
  const handleDeleteClick = (e: React.MouseEvent, appId: string) => {
    e.stopPropagation(); // 阻止事件冒泡
    setMenuOpen(null); // 关闭菜单
    setDeleteConfirmOpen(appId); // 打开删除确认对话框
  };

  // 处理删除确认
  const handleDeleteConfirm = async (appId: string) => {
    try {
      setIsLoading(true);

      // 调用删除API
      await deleteApp(appId);

      // 从列表中移除已删除的应用
      setApps(prevApps => prevApps.filter(app => app.id !== appId));

      // 显示成功消息
      showSuccess("アプリが正常に削除されました");
    } catch (error) {
      console.error("Failed to delete app:", error);
      showError(`アプリの削除に失敗しました: ${error instanceof Error ? error.message : '未知のエラー'}`);
    } finally {
      setDeleteConfirmOpen(null); // 关闭删除确认对话框
      setIsLoading(false);
    }
  };

  // 处理删除取消
  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(null); // 关闭删除确认对话框
  };

  // 关闭所有菜单
  const handleClickOutside = () => {
    if (menuOpen) {
      setMenuOpen(null);
    }
  };

  // 所有应用列表，包括创建应用卡片
  const allApps = [createAppCard, ...apps];

  // 过滤应用
  const filteredApps = allApps.filter(app =>
    (app.title.includes(search) || app.desc.includes(search))
  );

  return (
    <div className="categoryRoot" onClick={handleClickOutside}>
      <div className="categoryContainer">
        {/* カテゴリタブ */}
        <div className="categoryTabs">
          <button
            className="categoryTabButton categoryTabButtonActive"
          >
            <div className="categoryTabContent">
              <FcFlowChart className="categoryIcon" />
              <span>ワークフロー</span>
            </div>
          </button>

        {/* 検索バー */}
        <div className="searchBar">
          <input
            type="text"
            placeholder="検索..."
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="searchInput"
          />
        </div>

        </div>
        {/* アプリカード一覧 */}
        <div className="appList">
          {isLoading ? (
            <div className="loading">読み込み中...</div>
          ) : filteredApps.length === 0 ? (
            <div className="noApp">該当するアプリがありません。</div>
          ) : (
            filteredApps.map((app, idx) => (
              <div
                key={idx}
                className={`appCard ${app.isCreateCard ? 'createAppCard' : ''}`}
                onClick={() => handleAppCardClick(app)}
              >
                <div className="appIcon">
                  {app.isCreateCard ? (
                    <span className="createIcon">+</span>
                  ) : app.icon && app.icon.startsWith('http') ? (
                    <img src={app.icon} alt={app.title} className="appIconImage" />
                  ) : (
                    <span>{app.icon}</span>
                  )}
                </div>
                <div className="appTitle">{app.title}</div>
                <div className="appDesc">{app.desc}</div>
                <div className="appTag">タグ: {app.tag}</div>

                {/* 菜单按钮 - 只在非创建卡片上显示 */}
                {!app.isCreateCard && app.id && (
                  <div className="appMenuContainer">
                    <button
                      className="appMenuButton"
                      onClick={(e) => handleMenuClick(e, app.id!)}
                    >
                      <FiMoreVertical />
                    </button>

                    {/* 菜单弹出框 */}
                    {menuOpen === app.id && (
                      <div className="appMenu">
                        <button
                          className="appMenuItem"
                          onClick={(e) => handleDeleteClick(e, app.id!)}
                        >
                          削除
                        </button>
                      </div>
                    )}
                  </div>
                )}

                {/* 删除确认对话框 */}
                {deleteConfirmOpen === app.id && (
                  <div className="deleteConfirmOverlay">
                    <div className="deleteConfirmDialog">
                      <h3>アプリを削除しますか？</h3>
                      <p>この操作は元に戻せません。</p>
                      <div className="deleteConfirmButtons">
                        <button
                          className="cancelButton"
                          onClick={handleDeleteCancel}
                        >
                          キャンセル
                        </button>
                        <button
                          className="deleteButton"
                          onClick={() => handleDeleteConfirm(app.id!)}
                        >
                          削除
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>

      {/* 创建应用弹窗 */}
      <CreateAppModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreateApp={handleCreateApp}
      />
    </div>
  );
};

export default Apps;
