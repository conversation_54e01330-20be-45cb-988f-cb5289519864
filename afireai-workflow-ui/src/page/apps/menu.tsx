import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import './css/menu.css';
import {
  FaRobot, FaProjectDiagram, FaCalendarAlt, FaRegClock, FaCompress, FaRegListAlt, FaRegPlayCircle,
} from 'react-icons/fa'; // Using FaRegClock as FaClock might not exist in older fa versions, FaProjectDiagram for top right

import { FcWorkflow, FcTreeStructure, FcNeutralDecision, FcList } from "react-icons/fc";
import { AppResponse } from '../../services/appService';

// 定义组件属性类型
interface MenuProps {
  appInfo: AppResponse | null;
}

const Menu: React.FC<MenuProps> = ({ appInfo }) => {
  const [isCompact, setIsCompact] = useState<boolean>(false);
  const [activeMenu, setActiveMenu] = useState<string>('');
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams<{ id: string }>();

  useEffect(() => {
    // 根据当前 URL 路径设置活动菜单项
    const path = location.pathname;
    if (path.includes('app/workflow')) {
      setActiveMenu('workflow');
    } else if (path.includes('app/develop')) {
      setActiveMenu('develop');
    } else if (path.includes('app/logs')) {
      setActiveMenu('logs');
    } else {
      setActiveMenu('');
    }
  }, [location.pathname]);

  const toggleCompact = (): void => {
    setIsCompact(!isCompact);
  };

  const handleNavigate = (path: string): void => {
    navigate(path);
  };

  return (
    <div className={`menuContainer ${isCompact ? 'compact' : ''}`}>
      <div className="header">
        <div className="headerInfo">
          {appInfo && appInfo.app_icon_file && appInfo.app_icon_file.startsWith('http') ? (
            <img
              src={appInfo.app_icon_file}
              alt={appInfo?.app_name || 'App Icon'}
              className="headerIconImage"
            />
          ) : (
            <FcNeutralDecision className="headerIcon" />
          )}
          {!isCompact && (
          <div className="headerText">
            <span className="title">{appInfo?.app_name || 'アプリ'}</span>
            <span className="subtitle">{appInfo?.app_description || 'ワークフロー'}</span>
          </div>
        )}
        </div>
        <FaProjectDiagram className="optionsIcon" />
      </div>
      <ul className="menuList">
        <li
          className={`menuItem ${isCompact ? 'compact' : ''} ${activeMenu === 'workflow' ? 'active' : ''}`}
          onClick={() => handleNavigate(`/app/workflow/${id}`)}
        >
          <FcWorkflow className="menuIcon" /> {!isCompact && 'オーケストレート'}
        </li>
        <li
          className={`menuItem ${isCompact ? 'compact' : ''} ${activeMenu === 'develop' ? 'active' : ''}`}
          onClick={() => handleNavigate(`/app/develop/${id}`)}
        >
          <FcTreeStructure className="menuIcon" /> {!isCompact && 'APIアクセス'}
        </li>
        <li
          className={`menuItem ${isCompact ? 'compact' : ''} ${activeMenu === 'logs' ? 'active' : ''}`}
          onClick={() => handleNavigate(`/app/logs/${id}`)}
        >
          <FcList className="menuIcon" /> {!isCompact && 'ログ'}
        </li>
      </ul>
      <div className="footer" onClick={toggleCompact}>
        <FaCompress className="footerIcon" />
      </div>
    </div>
  );
};

export default Menu;
