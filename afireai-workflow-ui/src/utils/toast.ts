import { toast, ToastOptions } from 'react-toastify';

// 默认配置
const defaultOptions: ToastOptions = {
  position: "top-right",
  autoClose: 3000,
  hideProgressBar: false, // 改为false，让ToastContainer控制
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
};

// 成功提示
export const showSuccess = (message: string, options?: ToastOptions) => {
  return toast.success(message, { ...defaultOptions, ...options });
};

// 错误提示
export const showError = (message: string, options?: ToastOptions) => {
  return toast.error(message, { ...defaultOptions, ...options });
};

// 信息提示
export const showInfo = (message: string, options?: ToastOptions) => {
  return toast.info(message, { ...defaultOptions, ...options });
};

// 警告提示
export const showWarning = (message: string, options?: ToastOptions) => {
  return toast.warning(message, { ...defaultOptions, ...options });
};

// 默认提示
export const showToast = (message: string, options?: ToastOptions) => {
  return toast(message, { ...defaultOptions, ...options });
};

// 测试autoClose功能
export const testAutoClose = () => {
  console.log('Testing autoClose functionality...');

  // 测试1: 1秒自动关闭
  showError('测试1: 1秒后自动关闭', { autoClose: 1000 });

  // 测试2: 3秒自动关闭（默认）
  setTimeout(() => {
    showSuccess('测试2: 3秒后自动关闭（默认）');
  }, 500);

  // 测试3: 5秒自动关闭
  setTimeout(() => {
    showWarning('测试3: 5秒后自动关闭', { autoClose: 5000 });
  }, 1000);

  // 测试4: 不自动关闭
  setTimeout(() => {
    showInfo('测试4: 不会自动关闭', { autoClose: false });
  }, 1500);

  console.log('AutoClose tests started. Check if toasts close automatically.');
};
