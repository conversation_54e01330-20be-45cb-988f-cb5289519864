import React, { useState, useEffect } from "react";
import { FaTimes } from 'react-icons/fa';  // 添加 FaTimes の导入

import './css/CreateAppModal.css'; // 引入 CreateAppModal の CSS ファイル

interface UpdateAppModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateApp: (appId: string, appName: string, appDescription: string, iconFile?: File) => Promise<void>;  // 新增iconFile参数
  initialName: string;
  initialDescription: string;
  appId: string;
}

const UpdateAppModal: React.FC<UpdateAppModalProps> = ({
  isOpen,
  onClose,
  onUpdateApp,
  initialName,
  initialDescription,
  appId
}) => {
  const [appName, setAppName] = useState(initialName);
  const [appDescription, setAppDescription] = useState(initialDescription);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);  // 新增ファイル状态

  useEffect(() => {
    setAppName(initialName);
    setAppDescription(initialDescription);
  }, [initialName, initialDescription]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await onUpdateApp(appId, appName, appDescription, selectedFile);  // 传递ファイルパラメータ
      onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="create-app-modal">
        <div className="modal-header">
          <h2>アプリ情報を編集する</h2>
          <button className="close-button" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {/* アプリのアイコンと名前 */}
          <div className="form-group">
            <label>アプリのアイコンと名前</label>
            <div className="icon-name-container">
              <div className="icon-upload-container">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                  id="icon-upload"
                  style={{ display: 'none' }}
                />
                <label htmlFor="icon-upload" className="icon-preview">
                  {selectedFile ? (
                    <img
                      src={URL.createObjectURL(selectedFile)}
                      alt="App Icon"
                      className="icon-image"
                    />
                  ) : (
                    <div className="default-icon">
                      🤖
                    </div>
                  )}
                </label>
              </div>
              <input
                type="text"
                value={appName}
                onChange={(e) => setAppName(e.target.value)}
                required
                className="app-name-inline"
                placeholder="アプリ名を入力"
              />
            </div>
          </div>
          <div className="form-group">
            <label>説明</label>
            <textarea
              value={appDescription}
              onChange={(e) => setAppDescription(e.target.value)}
              placeholder="アプリの説明を入力してください"
              className="app-description-input"
            />
          </div>
          <div className="modal-footer">
            <button type="button" className="cancel-button" onClick={onClose} disabled={isSubmitting}>
              キャンセル
            </button>
            <button type="submit" className="create-button" disabled={isSubmitting}>
              {isSubmitting ? "保存中..." : "保存"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateAppModal;