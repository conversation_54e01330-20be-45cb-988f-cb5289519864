import React, { useState, useEffect } from "react";
import { FaTimes } from 'react-icons/fa';  // 添加 FaTimes の导入

import './css/CreateAppModal.css'; // 引入 CreateAppModal の CSS ファイル

interface UpdateAppModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateApp: (appId: string, appName: string, appDescription: string, iconFile?: File) => Promise<void>;  // 新增iconFile参数
  initialName: string;
  initialDescription: string;
  appId: string;
}

const UpdateAppModal: React.FC<UpdateAppModalProps> = ({
  isOpen,
  onClose,
  onUpdateApp,
  initialName,
  initialDescription,
  appId
}) => {
  const [appName, setAppName] = useState(initialName);
  const [appDescription, setAppDescription] = useState(initialDescription);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);  // 新增ファイル状态

  useEffect(() => {
    setAppName(initialName);
    setAppDescription(initialDescription);
  }, [initialName, initialDescription]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await onUpdateApp(appId, appName, appDescription, selectedFile);  // 传递ファイルパラメータ
      onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="create-app-modal">
        <div className="modal-header">
          <h2>アプリを更新</h2>
          <button className="close-button" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {/* 新增アイコンアップロード区块 */}
          <div className="form-group">
            <label>アプリアイコン</label>
            <input
              type="file"
              accept="image/*"
              onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
            />
            {/* 添加图标预览 */}
            {selectedFile && (
              <img
                src={URL.createObjectURL(selectedFile)}
                alt="Preview"
                style={{ maxWidth: '100px', marginTop: '10px' }}
              />
            )}
          </div>
          <div className="form-group">
            <label>アプリ名</label>
            <input
              type="text"
              value={appName}
              onChange={(e) => setAppName(e.target.value)}
              required
            />
          </div>
          <div className="form-group">
            <label>説明</label>
            <textarea
              value={appDescription}
              onChange={(e) => setAppDescription(e.target.value)}
            />
          </div>
          <div className="modal-footer">
            <button type="button" className="cancel-button" onClick={onClose} disabled={isSubmitting}>
              キャンセル
            </button>
            <button type="submit" className="create-button" disabled={isSubmitting}>
              {isSubmitting ? "更新中..." : "更新"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateAppModal;