// src/router.tsx
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Apps from './page/apps/apps';
import AppWorkflow from './page/apps/appWorkflow.tsx';
import AppDevelop from './page/apps/appDevelop.tsx';
import AppLogs from './page/apps/appLogs.tsx';
import Datasets from './page/datasets/dataset';
import DatasetCreate from './page/datasets/create';
import DatasetExtract from './page/datasets/extract';
import DatasetSettings from './page/datasets/settings';
import SignIn from './page/auth/SignIn';

import Tools from './page/tools/Tools';
import { isAuthenticated } from './services/authService';

// 受保护的路由组件
const ProtectedRoute = ({ children }: { children: React.ReactElement }) => {
  if (!isAuthenticated()) {
    // 如果未登录，重定向到登录页面
    return <Navigate to="/signin" replace />;
  }
  return children;
};

const AppRoutes = () => {
  return (
    <Routes>
      {/* 公共路由 */}
      <Route path="/signin" element={<SignIn />} />
      <Route path="/" element={<Navigate to="/signin" replace />} />

      {/* 受保护的路由 */}
      <Route path="/apps" element={
        <ProtectedRoute>
          <Apps />
        </ProtectedRoute>
      } />
      <Route path="/app/workflow/:id" element={
        <ProtectedRoute>
          <AppWorkflow />
        </ProtectedRoute>
      } />
      <Route path="/app/develop/:id" element={
        <ProtectedRoute>
          <AppDevelop />
        </ProtectedRoute>
      } />
      <Route path="/app/logs/:id" element={
        <ProtectedRoute>
          <AppLogs />
        </ProtectedRoute>
      } />
      <Route path="/datasets" element={
        <ProtectedRoute>
          <Datasets />
        </ProtectedRoute>
      } />
      <Route path="/tools" element={
        <ProtectedRoute>
          <Tools />
        </ProtectedRoute>
      } />
      <Route path="/datasets/create" element={
        <ProtectedRoute>
          <DatasetCreate />
        </ProtectedRoute>
      } />
      <Route path="/datasets/extract" element={
        <ProtectedRoute>
          <DatasetExtract />
        </ProtectedRoute>
      } />
      <Route path="/datasets/settings" element={
        <ProtectedRoute>
          <DatasetSettings />
        </ProtectedRoute>
      } />

      {/* 未匹配的路由重定向到登录页面 */}
      <Route path="*" element={<Navigate to="/signin" replace />} />
    </Routes>
  );
}

export default AppRoutes;
