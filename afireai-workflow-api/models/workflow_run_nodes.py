# models/workflow_run_nodes.py

from sqlalchemy import Column, String, Text, DateTime
from database.db import Base
from datetime import datetime, timezone

class WorkflowRunNode(Base):
    __tablename__ = "workflow_run_nodes"

    id = Column(String(36), primary_key=True, nullable=False)  # ID
    user_id = Column(String(36), nullable=False)  # ユーザーID
    workflow_run_id = Column(String(36), nullable=False)  # work flow run id
    node_id = Column(String(36), nullable=False)  # ノードID
    node_ver = Column(String(36), nullable=False)  # ノードVer
    status = Column(String(255))  # 実行状態
    params = Column(String(255))  # パラメーター
    memory = Column(String(50))  # メモリ
    flow_json = Column(Text, nullable=False)  # JSON
    output_type = Column(String(2), nullable=False)  # レスポンスtype
    output = Column(Text, nullable=False)  # レスポンス
    started_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)  # 開始時刻
    finished_at = Column(DateTime, nullable=True)  # 終了時刻
    ip_addr = Column(String(20), nullable=False)  # IPアドレス
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)  # 作成タイムスタンプ
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False, onupdate=lambda: datetime.now(timezone.utc))  # 更新タイムスタンプ
