# models/apps.py

from sqlalchemy import Column, String, Text, DateTime
from database.db import Base
from datetime import datetime

class App(Base):
    __tablename__ = "apps"

    user_id = Column(String(36), nullable=False)  # ユーザーID
    app_id = Column(String(36), primary_key=True, nullable=False)  # アプリID
    app_name = Column(String(255))  # アプリ名称
    app_description = Column(String(255))  # アプリ説明
    app_icon_file = Column(String(255))  # アプリICON
    tag = Column(String(50))  # タグ
    flow_json = Column(Text, nullable=False)  # JSON
    ip_addr = Column(String(20), nullable=False)  # IPアドレス
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)  # 作成タイムスタンプ
    updated_at = Column(DateTime, default=datetime.utcnow, nullable=False, onupdate=datetime.utcnow)  # 更新タイムスタンプ
