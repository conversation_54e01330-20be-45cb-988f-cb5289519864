{"description": "Node type to API endpoint mapping configuration", "version": "1.0", "last_updated": "2025-05-26", "endpoint_map": {"startNode": "run/v1/startNode", "endNode": "run/v1/endNode", "llmNode": "run/v1/llmNode", "datasetGrepNode": "run/v1/datasetGrepNode", "httpRequestNode": "run/v1/httpRequestNode"}, "node_descriptions": {"startNode": "Workflow start node - initiates the workflow execution", "endNode": "Workflow end node - terminates the workflow execution", "llmNode": "Large Language Model node - handles AI text generation", "datasetGrepNode": "Dataset search node - searches through datasets", "httpRequestNode": "HTTP request node - makes external API calls"}}