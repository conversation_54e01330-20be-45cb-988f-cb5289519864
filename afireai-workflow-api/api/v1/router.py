# api/v1/router.py

from fastapi import APIRouter
from .start_node import router as start_node_router
from .end_node import router as end_node_router
from .llm_node import router as llm_node_router
from .dataset_grep_node import router as dataset_grep_node_router
from .http_request_node import router as http_request_node_router

# 创建v1版本的主路由
v1_router = APIRouter()

# 包含所有节点路由
v1_router.include_router(start_node_router, tags=["Start Node"])
v1_router.include_router(end_node_router, tags=["End Node"])
v1_router.include_router(llm_node_router, tags=["LLM Node"])
v1_router.include_router(dataset_grep_node_router, tags=["Dataset Grep Node"])
v1_router.include_router(http_request_node_router, tags=["HTTP Request Node"])
