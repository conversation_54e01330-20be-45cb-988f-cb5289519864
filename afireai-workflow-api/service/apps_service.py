# service/apps_service.py

import uuid
from sqlalchemy.orm import Session
from sqlalchemy import select
from fastapi import HTTPException, status
from models.apps import App
from schemas.apps import AppCreate, AppUpdate
from datetime import datetime
from typing import List, Optional

from utils.auth import get_current_user

# Function IDs
FUNC_CREATE_APP = "APP001"
FUNC_UPDATE_APP = "APP002"
FUNC_GET_APP = "APP003"
FUNC_LIST_APPS = "APP004"
FUNC_DELETE_APP = "APP005"

def create_app(db: Session, app_data: AppCreate) -> App:
    """
    新しいアプリを作成する

    Args:
        db (Session): データベースセッション
        app_data (AppCreate): 作成するアプリのデータ

    Returns:
        App: 作成されたアプリのモデル
    """
    app_id = str(uuid.uuid4())

    db_app = App(
        user_id=app_data.user_id,
        app_id=app_id,
        app_name=app_data.app_name,
        app_description=app_data.app_description,
        app_icon_file=app_data.app_icon_file,
        tag=app_data.tag,
        flow_json=app_data.flow_json,
        ip_addr=app_data.ip_addr
    )

    db.add(db_app)
    db.commit()
    db.refresh(db_app)

    return db_app

def update_app(db: Session, app_id: str, app_data: AppUpdate) -> Optional[App]:
    """
    既存のアプリを更新する

    Args:
        db (Session): データベースセッション
        app_id (str): 更新するアプリのID
        app_data (AppUpdate): 更新するデータ

    Returns:
        Optional[App]: 更新されたアプリのモデル、存在しない場合はNone
    """
    db_app = db.query(App).filter(App.app_id == app_id).first()

    if not db_app:
        return None

    # 更新するフィールドを設定
    if app_data.app_name is not None:
        db_app.app_name = app_data.app_name

    if app_data.app_description is not None:
        db_app.app_description = app_data.app_description

    if app_data.app_icon_file is not None:
        db_app.app_icon_file = app_data.app_icon_file

    if app_data.tag is not None:
        db_app.tag = app_data.tag

    if app_data.flow_json is not None:
        db_app.flow_json = app_data.flow_json

    db_app.updated_at = datetime.utcnow()

    db.commit()
    db.refresh(db_app)

    return db_app

def get_app(db: Session, user_id: str, app_id: str) -> Optional[App]:
    """
    获取指定用户和app_id的应用
    修改查询条件：同时验证user_id和app_id
    """
    return db.query(App).filter(
        App.app_id == app_id,
        App.user_id == user_id  # 添加用户ID过滤条件
    ).first()

def get_apps_by_user(db: Session, user_id: str) -> List[App]:
    """
    指定されたユーザーIDに関連するすべてのアプリを取得する

    Args:
        db (Session): データベースセッション
        user_id (str): ユーザーID

    Returns:
        List[App]: アプリのリスト
    """
    return db.query(App).filter(App.user_id == user_id).all()

def delete_app(db: Session, app_id: str) -> bool:
    """
    指定されたIDのアプリを削除する

    Args:
        db (Session): データベースセッション
        app_id (str): 削除するアプリのID

    Returns:
        bool: 削除に成功した場合はTrue、アプリが存在しない場合はFalse
    """
    db_app = db.query(App).filter(App.app_id == app_id).first()

    if not db_app:
        return False

    db.delete(db_app)
    db.commit()

    return True
